import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koc_app/src/base/cubit/base_cubit_state.dart';
import 'package:koc_app/src/modules/report/data/model/campaign/campaign_top_ten_click_response.dart';
import 'package:koc_app/src/modules/report/data/model/payment/minimum_payment_details.dart';
import 'package:koc_app/src/modules/report/data/model/payment/payment_summary.dart';
import 'package:koc_app/src/modules/report/data/model/performance_monthly_report_data.dart';
import 'package:koc_app/src/modules/report/data/model/report_model.dart';
import 'package:koc_app/src/modules/survey/data/models/survey_info.dart';

part 'report_state.freezed.dart';
part 'report_state.g.dart';

@freezed
class ReportState extends BaseCubitState with _$ReportState {
  factory ReportState({
    @Default({}) Map<DateTime, int> currentOneYearClickCount,
    @Default({}) Map<DateTime, int> currentOneYearConversionCount,
    @Default([]) List<PerformanceMonthlyReportData> performanceMonthlyData,
    @Default(PerformanceChartTitle.CLICKS) PerformanceChartTitle leftChartTitle,
    @Default(PerformanceChartTitle.CONVERSIONS) PerformanceChartTitle rightChartTitle,
    @Default(0) int thisMonthOccurredConversionCount,
    @Default(0) double lastMonthApprovedReward,
    @Default([]) List<CampaignNameAndClicks> topTenCampaignsClickCount,
    PaymentSummary? paymentSummary,
    MinimumPaymentDetails? minimumPaymentDetails,
    @Default('') String currency,
    @Default('') String currencyCode,
    @Default('') String errorMessage,
    @Default(false) bool isPullToRefresh,
    Country? country,
    @Default(0) int selectedSiteId,
  }) = _PerformanceReportState;

  factory ReportState.fromJson(Map<String, Object?> json) => _$ReportStateFromJson(json);
}
